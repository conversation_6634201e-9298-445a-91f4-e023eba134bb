<?php

namespace App\Settings;

use App\Data\TelematicSettingData;
use <PERSON><PERSON>\LaravelSettings\Settings;

class SamsaraSettings extends Settings implements TelematicSettingInterface
{
    public const string PROVIDER_KEY = 'samsara';

    public bool $enabled;

    public array $credentials;

    public static function group(): string
    {
        return 'integrations_telematics_samsara';
    }

    public function getSettings(): array
    {
        return [
            'id' => self::PROVIDER_KEY,
            'name' => 'Samsara',
            'enabled' => $this->enabled,
            'fields' => [
                [
                    'key' => 'api_key',
                    'value' => data_get($this->credentials, 'api_key'),
                    'label' => 'API Key',
                    'type' => 'password',
                    'required' => true,
                    'placeholder' => 'Enter your Samsara API key',
                    'description' => 'You can find your API key in the Samsara dashboard under Settings > API Tokens',
                ],
            ],
        ];
    }

    public function update(TelematicSettingData $settingData): void
    {
        $this->enabled = $settingData->enabled;
        $this->credentials = $settingData->credentials;

        $this->save();
    }
}
