<?php

namespace App\Console\Commands;

use App\Services\Telematics\Sync\TelematicSyncService;
use Illuminate\Console\Command;

class InitialTelematicSyncCommand extends Command
{
    public function __construct(
        private readonly TelematicSyncService $telematicSyncService,
    ) {
        parent::__construct();
    }

    /**
     * {@inheritDoc}
     */
    protected $signature = 'sync:trucks';

    /**
     * {@inheritDoc}
     */
    protected $description = 'Sync initial entities from enabled telematics provider';

    public function handle(): int
    {
        $this->info('Starting initial entity sync from enabled provider');

        $this->telematicSyncService->syncInitial();

        $this->info('Initial entities were synced successfully.');

        return self::SUCCESS;
    }
}
