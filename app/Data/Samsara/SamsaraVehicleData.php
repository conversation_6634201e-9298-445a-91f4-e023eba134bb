<?php

namespace App\Data\Samsara;

use <PERSON><PERSON>\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;
use <PERSON>tie\LaravelData\Mappers\SnakeCaseMapper;

class SamsaraVehicleData extends Data
{
    public function __construct(
        public string $id,
        public string $vin,
        public string $name,
        public ?string $licensePlate,
        public string $createdAtTime,
        public ?string $updatedAtTime = null,
        public ?array $tags = null,
        public ?string $make = null,
        public ?string $model = null,
        public ?int $year = null,
    ) {}


}
