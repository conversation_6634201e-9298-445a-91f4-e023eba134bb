<?php

namespace App\Data\Samsara;

use <PERSON><PERSON>\LaravelData\Attributes\MapName;
use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Mappers\SnakeCaseMapper;

#[MapName(SnakeCaseMapper::class)]
class SamsaraVehicleData extends Data
{
    public function __construct(
        public string $id,
        public string $vin,
        public string $name,
        public ?string $licensePlate,
        public string $createdAtTime,
        public ?string $updatedAtTime = null,
        public ?array $tags = null,
        public ?string $make = null,
        public ?string $model = null,
        public ?int $year = null,
    ) {}

    public static function fromSamsaraResponse(array $vehicleData): self
    {
        return new self(
            id: $vehicleData['id'],
            vin: $vehicleData['vin'],
            name: $vehicleData['name'] ?? '',
            licensePlate: $vehicleData['licensePlate'] ?? null,
            createdAtTime: $vehicleData['createdAtTime'],
            updatedAtTime: $vehicleData['updatedAtTime'] ?? null,
            tags: $vehicleData['tags'] ?? null,
            make: $vehicleData['make'] ?? null,
            model: $vehicleData['model'] ?? null,
            year: $vehicleData['year'] ?? null,
        );
    }
}
