<?php

namespace App\Data\Samsara;

use Spa<PERSON>\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

class SamsaraVehicleResponseData extends Data
{
    public function __construct(
        #[DataCollectionOf(SamsaraVehicleData::class)]
        public DataCollection $data,
        public SamsaraPaginationData $pagination,
    ) {}

    public static function fromApiResponse(array $response): self
    {
        return new self(
            data: SamsaraVehicleData::collection($response['data'] ?? []),
            pagination: SamsaraPaginationData::from($response['pagination'] ?? []),
        );
    }
}
