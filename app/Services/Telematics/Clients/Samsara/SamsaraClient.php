<?php

namespace App\Services\Telematics\Clients\Samsara;

use App\Settings\SamsaraSettings;
use Exception;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SamsaraClient
{
    private const string VEHICLES_ENDPOINT = 'fleet/vehicles';

    public function __construct(
        private PendingRequest $client,
        private readonly SamsaraSettings $settings,
    ) {
        $baseUrl = config('services.samsara.base_url');

        $this->client = Http::withHeaders([
            'Authorization' => "Bearer {$this->settings->credentials['api_key']}",
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->baseUrl($baseUrl);
    }

    public function getAllVehicles(array $params = []): ?array
    {
        try {
            $response = $this->client->get(self::VEHICLES_ENDPOINT, $params);

            if ($response->failed()) {
                Log::error('Samsara API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'params' => $params,
                ]);

                return null;
            }

            $data = $response->json();

            return [
                'data' => $data['data'] ?? [],
                'pagination' => $data['pagination'] ?? [],
            ];
        } catch (Exception $e) {
            Log::error('Samsara API exception', [
                'message' => $e->getMessage(),
                'params' => $params,
            ]);

            return null;
        }
    }
}
