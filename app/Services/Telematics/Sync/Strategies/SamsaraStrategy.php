<?php

namespace App\Services\Telematics\Sync\Strategies;

use App\Models\ExternalEntityMapping;
use App\Models\Truck;
use App\Services\Telematics\Clients\Samsara\SamsaraClient;
use App\Services\Telematics\Sync\TelematicSyncInterface;
use App\Settings\SamsaraSettings;
use Exception;
use Illuminate\Support\Facades\Log;

class SamsaraStrategy implements TelematicSyncInterface
{
    private const string SERVICE_TYPE = 'telematic';

    public function __construct(
        private readonly SamsaraClient $client,
    ) {}

    public function syncInitial(): void
    {
        $endCursor = null;
        $totalTrucksSynced = 0;

        do {
            $params = [
                'after' => $endCursor,
            ];

            $response = $this->client->getAllVehicles($params);

            if ($response === null) {
                Log::error('Invalid response from Samsara API');

                throw new Exception('Invalid response from Samsara API');
            }

            $endCursor = data_get($response, 'pagination.endCursor');
            $hasNextPage = data_get($response, 'pagination.hasNextPage');

            $syncedCount = $this->syncVehicles($response['data']);
            $totalTrucksSynced += $syncedCount;

        } while ($hasNextPage && $endCursor !== '');

        Log::info("Truck sync completed. Total synced: {$totalTrucksSynced}");
    }

    private function syncVehicles(array $vehicles): int
    {
        $syncedCount = 0;

        $samsaraVinNumbers = collect($vehicles)->pluck('vin')->unique()->all();
        $trucks = Truck::whereIn('vin', $samsaraVinNumbers)->with('samsaraMappings')->get();
        $vehiclesByVin = collect($vehicles)->keyBy('vin');

        foreach ($trucks as $truck) {
            $samsaraTruckData = $vehiclesByVin->get($truck->vin);

            if ($truck->samsaraMappings->isNotEmpty()) {
                $existingMapping = $truck->samsaraMappings->first();
                $existingCreatedAt = data_get($existingMapping->metadata, 'createdAt');

                // Skip if Samsara data is older than what we already have
                if ($existingCreatedAt >= $samsaraTruckData['createdAtTime']) {
                    continue;
                }

            }

            ExternalEntityMapping::updateOrCreate([
                'service_type' => self::SERVICE_TYPE,
                'provider' => SamsaraSettings::PROVIDER_KEY,
                'entity_type' => Truck::class,
                'entity_id' => $truck->id,
                'external_id' => $samsaraTruckData['id'],
            ], [
                'metadata' => $this->getMetadata($samsaraTruckData),
            ]);

            $syncedCount++;
        }

        return $syncedCount;
    }

    private function getMetadata(array $samsaraTruckData): array
    {
        return [
            'createdAt' => $samsaraTruckData['createdAtTime'] ?? null,
        ];
    }
}
