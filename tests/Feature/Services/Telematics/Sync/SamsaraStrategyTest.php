<?php

namespace Tests\Feature\Services\Telematics\Sync;

use App\Models\ExternalEntityMapping;
use App\Models\Truck;
use App\Services\Telematics\Sync\Strategies\SamsaraStrategy;
use App\Settings\SamsaraSettings;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Set up Samsara settings
    $this->samsaraSettings = app(SamsaraSettings::class);
    $this->samsaraSettings->enabled = true;
    $this->samsaraSettings->credentials = ['api_key' => 'test_api_key'];
    $this->samsaraSettings->save();

    // Mock HTTP responses
    Http::fake();
});

test('syncs new trucks from samsara api successfully', function () {
    // Create trucks in database
    $truck1 = Truck::factory()->create(['vin' => 'VIN123456789']);
    $truck2 = Truck::factory()->create(['vin' => 'VIN987654321']);

    // Mock Samsara API response
    Http::fake([
        'https://api.samsara.com/fleet/vehicles*' => Http::response([
            'data' => [
                [
                    'id' => 'samsara_vehicle_1',
                    'vin' => 'VIN123456789',
                    'name' => 'Truck 1',
                    'createdAtTime' => '2025-01-01T10:00:00Z',
                    'licensePlate' => 'ABC123',
                    'make' => 'Volvo',
                    'model' => 'VNL',
                    'year' => 2020,
                ],
                [
                    'id' => 'samsara_vehicle_2',
                    'vin' => 'VIN987654321',
                    'name' => 'Truck 2',
                    'createdAtTime' => '2025-01-01T11:00:00Z',
                    'licensePlate' => 'XYZ789',
                    'make' => 'Kenworth',
                    'model' => 'T680',
                    'year' => 2021,
                ],
            ],
            'pagination' => [
                'hasNextPage' => false,
                'endCursor' => null,
            ],
        ], 200),
    ]);

    $strategy = app(SamsaraStrategy::class);
    $strategy->syncInitial();

    // Assert mappings were created
    $this->assertDatabaseCount('external_entity_mappings', 2);

    $this->assertDatabaseHas('external_entity_mappings', [
        'service_type' => 'telematic',
        'provider' => 'samsara',
        'entity_type' => Truck::class,
        'entity_id' => $truck1->id,
        'external_id' => 'samsara_vehicle_1',
    ]);

    $this->assertDatabaseHas('external_entity_mappings', [
        'service_type' => 'telematic',
        'provider' => 'samsara',
        'entity_type' => Truck::class,
        'entity_id' => $truck2->id,
        'external_id' => 'samsara_vehicle_2',
    ]);

    // Verify metadata is stored correctly
    $mapping = ExternalEntityMapping::where('external_id', 'samsara_vehicle_1')->first();
    expect($mapping->metadata)->toEqual([
        'createdAt' => '2025-01-01T10:00:00Z',
    ]);
});

test('handles pagination correctly', function () {
    $truck1 = Truck::factory()->create(['vin' => 'VIN123456789']);
    $truck2 = Truck::factory()->create(['vin' => 'VIN987654321']);

    // Mock paginated responses
    Http::fake([
        'https://api.samsara.com/fleet/vehicles' => Http::response([
            'data' => [
                [
                    'id' => 'samsara_vehicle_1',
                    'vin' => 'VIN123456789',
                    'name' => 'Truck 1',
                    'createdAtTime' => '2025-01-01T10:00:00Z',
                ],
            ],
            'pagination' => [
                'hasNextPage' => true,
                'endCursor' => 'cursor_123',
            ],
        ], 200),
        'https://api.samsara.com/fleet/vehicles?after=cursor_123' => Http::response([
            'data' => [
                [
                    'id' => 'samsara_vehicle_2',
                    'vin' => 'VIN987654321',
                    'name' => 'Truck 2',
                    'createdAtTime' => '2025-01-01T11:00:00Z',
                ],
            ],
            'pagination' => [
                'hasNextPage' => false,
                'endCursor' => null,
            ],
        ], 200),
    ]);

    $strategy = app(SamsaraStrategy::class);
    $strategy->syncInitial();

    // Assert both pages were processed
    $this->assertDatabaseCount('external_entity_mappings', 2);
    Http::assertSentCount(2);
});

test('skips trucks with older samsara data', function () {
    $truck = Truck::factory()->create(['vin' => 'VIN123456789']);

    // Create existing mapping with newer timestamp
    ExternalEntityMapping::create([
        'service_type' => 'telematic',
        'provider' => 'samsara',
        'entity_type' => Truck::class,
        'entity_id' => $truck->id,
        'external_id' => 'old_samsara_vehicle_1',
        'metadata' => ['createdAt' => '2025-01-02T10:00:00Z'], // Newer
    ]);

    // Mock Samsara API with older data
    Http::fake([
        'https://api.samsara.com/fleet/vehicles*' => Http::response([
            'data' => [
                [
                    'id' => 'samsara_vehicle_1',
                    'vin' => 'VIN123456789',
                    'name' => 'Truck 1',
                    'createdAtTime' => '2025-01-01T10:00:00Z', // Older
                ],
            ],
            'pagination' => [
                'hasNextPage' => false,
                'endCursor' => null,
            ],
        ], 200),
    ]);

    $strategy = app(SamsaraStrategy::class);
    $strategy->syncInitial();

    // Assert mapping was not updated (still has old external_id)
    $this->assertDatabaseHas('external_entity_mappings', [
        'entity_id' => $truck->id,
        'external_id' => 'old_samsara_vehicle_1',
    ]);

    $this->assertDatabaseMissing('external_entity_mappings', [
        'entity_id' => $truck->id,
        'external_id' => 'samsara_vehicle_1',
    ]);
});

test('updates trucks with newer samsara data', function () {
    $truck = Truck::factory()->create(['vin' => 'VIN123456789']);

    // Create existing mapping with older timestamp
    ExternalEntityMapping::create([
        'service_type' => 'telematic',
        'provider' => 'samsara',
        'entity_type' => Truck::class,
        'entity_id' => $truck->id,
        'external_id' => 'old_samsara_vehicle_1',
        'metadata' => ['createdAt' => '2025-01-01T10:00:00Z'], // Older
    ]);

    // Mock Samsara API with newer data
    Http::fake([
        'https://api.samsara.com/fleet/vehicles*' => Http::response([
            'data' => [
                [
                    'id' => 'new_samsara_vehicle_1',
                    'vin' => 'VIN123456789',
                    'name' => 'Updated Truck 1',
                    'createdAtTime' => '2025-01-02T10:00:00Z', // Newer
                ],
            ],
            'pagination' => [
                'hasNextPage' => false,
                'endCursor' => null,
            ],
        ], 200),
    ]);

    $strategy = app(SamsaraStrategy::class);
    $strategy->syncInitial();

    // Assert mapping was updated with new external_id and metadata
    $this->assertDatabaseHas('external_entity_mappings', [
        'entity_id' => $truck->id,
        'external_id' => 'new_samsara_vehicle_1',
    ]);

    $mapping = ExternalEntityMapping::where('entity_id', $truck->id)->first();
    expect($mapping->metadata['createdAt'])->toBe('2025-01-02T10:00:00Z');

    // Assert only one mapping exists (updated, not duplicated)
    $this->assertDatabaseCount('external_entity_mappings', 1);
});

test('ignores trucks not found in database', function () {
    // Don't create any trucks in database

    Http::fake([
        'https://api.samsara.com/fleet/vehicles*' => Http::response([
            'data' => [
                [
                    'id' => 'samsara_vehicle_1',
                    'vin' => 'VIN_NOT_IN_DB',
                    'name' => 'Unknown Truck',
                    'createdAtTime' => '2025-01-01T10:00:00Z',
                ],
            ],
            'pagination' => [
                'hasNextPage' => false,
                'endCursor' => null,
            ],
        ], 200),
    ]);

    $strategy = app(SamsaraStrategy::class);
    $strategy->syncInitial();

    // Assert no mappings were created
    $this->assertDatabaseCount('external_entity_mappings', 0);
});

test('throws exception when samsara api returns null', function () {
    Http::fake([
        'https://api.samsara.com/fleet/vehicles*' => Http::response(null, 500),
    ]);

    $strategy = app(SamsaraStrategy::class);

    expect(fn() => $strategy->syncInitial())
        ->toThrow(Exception::class, 'Invalid response from Samsara API');
});

test('logs sync completion with correct count', function () {
    Log::fake();

    $truck = Truck::factory()->create(['vin' => 'VIN123456789']);

    Http::fake([
        'https://api.samsara.com/fleet/vehicles*' => Http::response([
            'data' => [
                [
                    'id' => 'samsara_vehicle_1',
                    'vin' => 'VIN123456789',
                    'name' => 'Truck 1',
                    'createdAtTime' => '2025-01-01T10:00:00Z',
                ],
            ],
            'pagination' => [
                'hasNextPage' => false,
                'endCursor' => null,
            ],
        ], 200),
    ]);

    $strategy = app(SamsaraStrategy::class);
    $strategy->syncInitial();

    Log::assertLogged('info', function ($message, $context) {
        return $message === 'Truck sync completed.' &&
               $context['provider'] === 'samsara' &&
               $context['total_synced'] === 1;
    });
});
